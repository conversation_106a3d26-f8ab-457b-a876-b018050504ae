# pack
[Git Source](https://github.com/lidofinance/community-staking-module/blob/efc92ba178845b0562e369d8d71b585ba381ab86/src/lib/GIndex.sol)


```solidity
function pack(uint256 gI, uint8 p) pure returns (GIndex);
```
**Parameters**

|Name|Type|Description|
|----|----|-----------|
|`gI`|`uint256`|Is a generalized index of a node in a tree.|
|`p`|`uint8`|Is a power of a tree level the node belongs to.|

**Returns**

|Name|Type|Description|
|----|----|-----------|
|`<none>`|`GIndex`|GIndex|


