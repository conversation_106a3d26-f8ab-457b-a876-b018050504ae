# fls
[Git Source](https://github.com/lidofinance/community-staking-module/blob/efc92ba178845b0562e369d8d71b585ba381ab86/src/lib/GIndex.sol)

*From Solady LibBit, see https://github.com/Vectorized/solady/blob/main/src/utils/LibBit.sol.*

*Find last set.
Returns the index of the most significant bit of `x`,
counting from the least significant bit position.
If `x` is zero, returns 256.*


```solidity
function fls(uint256 x) pure returns (uint256 r);
```

