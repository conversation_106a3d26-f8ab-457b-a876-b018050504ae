# IACL
[Git Source](https://github.com/lidofinance/community-staking-module/blob/efc92ba178845b0562e369d8d71b585ba381ab86/src/interfaces/IACL.sol)


## Functions
### grantPermission


```solidity
function grantPermission(address _entity, address _app, bytes32 _role) external;
```

### getPermissionManager


```solidity
function getPermissionManager(address _app, bytes32 _role) external view returns (address);
```

