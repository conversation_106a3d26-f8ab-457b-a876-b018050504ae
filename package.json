{"name": "community-staking-module", "version": "0.0.1", "description": "Community Staking Module for Lido protocol", "repository": "https://github.com/lidofinance/community-staking-module.git", "author": "Community Staking Team <<EMAIL>>", "license": "GPL-3.0", "private": true, "scripts": {"lint:solhint": "solhint './src/**/*.sol'", "lint:check": "prettier --check **.sol && yarn lint:solhint", "lint:fix": "prettier --write **.sol", "generate:diffyscan": "node script/generateDiffyscanContracts.js", "gindex": "node script/gindex.mjs"}, "dependencies": {"@lodestar/types": "^1.31.0", "@openzeppelin/contracts": "5.0.2", "@openzeppelin/contracts-upgradeable": "5.0.2", "@openzeppelin/merkle-tree": "^1.0.8", "ds-test": "https://github.com/dapphub/ds-test", "forge-std": "https://github.com/foundry-rs/forge-std.git#v1.9.6"}, "devDependencies": {"husky": "^8.0.3", "lint-staged": "^15.4.3", "prettier": "^3.0.3", "prettier-plugin-solidity": "^1.1.3", "solhint": "5.0.1"}, "lint-staged": {"*": "prettier --ignore-unknown --write", "src/**/*.sol": "solhint './src/**/*.sol'"}, "packageManager": "yarn@4.1.1"}