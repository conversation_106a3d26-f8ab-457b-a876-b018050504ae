[{"inputs": [{"internalType": "bytes32", "name": "moduleType", "type": "bytes32"}, {"internalType": "uint256", "name": "minSlashingPenaltyQuotient", "type": "uint256"}, {"internalType": "uint256", "name": "elRewardsStealingFine", "type": "uint256"}, {"internalType": "uint256", "name": "maxKeysPerOperatorEA", "type": "uint256"}, {"internalType": "uint256", "name": "maxKeyRemovalCharge", "type": "uint256"}, {"internalType": "address", "name": "lidoLocator", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "AlreadyActivated", "type": "error"}, {"inputs": [], "name": "AlreadyProposed", "type": "error"}, {"inputs": [], "name": "AlreadySubmitted", "type": "error"}, {"inputs": [], "name": "EmptyKey", "type": "error"}, {"inputs": [], "name": "ExitedKeysDecrease", "type": "error"}, {"inputs": [], "name": "ExitedKeysHigherThanTotalDeposited", "type": "error"}, {"inputs": [], "name": "FailedToSendEther", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidKeysCount", "type": "error"}, {"inputs": [], "name": "InvalidLength", "type": "error"}, {"inputs": [], "name": "InvalidReportData", "type": "error"}, {"inputs": [], "name": "InvalidVetKeysPointer", "type": "error"}, {"inputs": [], "name": "MaxSigningKeysCountExceeded", "type": "error"}, {"inputs": [], "name": "MethodCallIsNotAllowed", "type": "error"}, {"inputs": [], "name": "NodeOperatorDoesNotExist", "type": "error"}, {"inputs": [], "name": "NotAllowedToJoinYet", "type": "error"}, {"inputs": [], "name": "NotAllowedToRecover", "type": "error"}, {"inputs": [], "name": "NotEnoughKeys", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "NotSupported", "type": "error"}, {"inputs": [], "name": "PauseUntilMustBeInFuture", "type": "error"}, {"inputs": [], "name": "PausedExpected", "type": "error"}, {"inputs": [], "name": "QueueIsEmpty", "type": "error"}, {"inputs": [], "name": "QueueLookupNoLimit", "type": "error"}, {"inputs": [], "name": "ResumedExpected", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "SenderIsNotEligible", "type": "error"}, {"inputs": [], "name": "SenderIsNotManagerAddress", "type": "error"}, {"inputs": [], "name": "SenderIsNotProposedAddress", "type": "error"}, {"inputs": [], "name": "SenderIsNotRewardAddress", "type": "error"}, {"inputs": [], "name": "SigningKeysInvalidOffset", "type": "error"}, {"inputs": [], "name": "StuckKeysHigherThanNonExited", "type": "error"}, {"inputs": [], "name": "ZeroAccounting<PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "ZeroAdminAddress", "type": "error"}, {"inputs": [], "name": "ZeroLocatorAddress", "type": "error"}, {"inputs": [], "name": "ZeroPauseDuration", "type": "error"}, {"inputs": [], "name": "ZeroRewardAddress", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "count", "type": "uint256"}], "name": "BatchEnqueued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "depositableKeysCount", "type": "uint256"}], "name": "DepositableSigningKeysCountChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "depositedKeysCount", "type": "uint256"}], "name": "DepositedSigningKeysCountChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ELRewardsStealingPenaltyCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ELRewardsStealingPenaltyCompensated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "proposedBlockHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "stolenAmount", "type": "uint256"}], "name": "ELRewardsStealingPenaltyReported", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "ELRewardsStealingPenaltySettled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ERC1155Recovered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ERC20Recovered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}], "name": "ERC721Recovered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EtherRecovered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "exitedKeysCount", "type": "uint256"}], "name": "ExitedSigningKeysCountChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "keyIndex", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "pubkey", "type": "bytes"}], "name": "InitialSlashingSubmitted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "KeyRemovalChargeApplied", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "KeyRemovalChargeSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "rewardAddress", "type": "address"}], "name": "NodeOperatorAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "oldProposedAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newProposedAddress", "type": "address"}], "name": "NodeOperatorManagerAddressChangeProposed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "NodeOperatorManagerAddressChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "oldProposedAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newProposedAddress", "type": "address"}], "name": "NodeOperatorRewardAddressChangeProposed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "NodeOperatorRewardAddressChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}], "name": "NonceChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [], "name": "PublicRelease", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "referrer", "type": "address"}], "name": "ReferrerSet", "type": "event"}, {"anonymous": false, "inputs": [], "name": "Resumed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "pubkey", "type": "bytes"}], "name": "SigningKeyAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "pubkey", "type": "bytes"}], "name": "SigningKeyRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "StETHSharesRecovered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "stuckKeysCount", "type": "uint256"}], "name": "StuckSigningKeysCountChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "targetLimitMode", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "targetValidatorsCount", "type": "uint256"}], "name": "TargetValidatorsCountChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalKeysCount", "type": "uint256"}], "name": "TotalSigningKeysCountChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "vettedKeysCount", "type": "uint256"}], "name": "VettedSigningKeysCountChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "VettedSigningKeysCountDecreased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "keyIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "pubkey", "type": "bytes"}], "name": "WithdrawalSubmitted", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EL_REWARDS_STEALING_FINE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "INITIAL_SLASHING_PENALTY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LIDO_LOCATOR", "outputs": [{"internalType": "contract ILidoLocator", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_KEY_REMOVAL_CHARGE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SIGNING_KEYS_PER_OPERATOR_BEFORE_PUBLIC_RELEASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAUSE_INFINITELY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAUSE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RECOVERER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REPORT_EL_REWARDS_STEALING_PENALTY_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RESUME_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "SETTLE_EL_REWARDS_STEALING_PENALTY_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "STAKING_ROUTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "STETH", "outputs": [{"internalType": "contract IStETH", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "VERIFIER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "accounting", "outputs": [{"internalType": "contract ICSAccounting", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "activatePublicRelease", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "keysCount", "type": "uint256"}, {"internalType": "bytes", "name": "publicKeys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}, {"components": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "bool", "name": "extendedManagerPermissions", "type": "bool"}], "internalType": "struct NodeOperatorManagementProperties", "name": "managementProperties", "type": "tuple"}, {"internalType": "bytes32[]", "name": "eaProof", "type": "bytes32[]"}, {"internalType": "address", "name": "referrer", "type": "address"}], "name": "addNodeOperatorETH", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "keysCount", "type": "uint256"}, {"internalType": "bytes", "name": "publicKeys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}, {"components": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "bool", "name": "extendedManagerPermissions", "type": "bool"}], "internalType": "struct NodeOperatorManagementProperties", "name": "managementProperties", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "internalType": "struct ICSAccounting.PermitInput", "name": "permit", "type": "tuple"}, {"internalType": "bytes32[]", "name": "eaProof", "type": "bytes32[]"}, {"internalType": "address", "name": "referrer", "type": "address"}], "name": "addNodeOperatorStETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "keysCount", "type": "uint256"}, {"internalType": "bytes", "name": "publicKeys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}, {"components": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "bool", "name": "extendedManagerPermissions", "type": "bool"}], "internalType": "struct NodeOperatorManagementProperties", "name": "managementProperties", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "internalType": "struct ICSAccounting.PermitInput", "name": "permit", "type": "tuple"}, {"internalType": "bytes32[]", "name": "eaProof", "type": "bytes32[]"}, {"internalType": "address", "name": "referrer", "type": "address"}], "name": "addNodeOperatorWstETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "keysCount", "type": "uint256"}, {"internalType": "bytes", "name": "publicKeys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}], "name": "addValidatorKeysETH", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "keysCount", "type": "uint256"}, {"internalType": "bytes", "name": "publicKeys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}, {"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "internalType": "struct ICSAccounting.PermitInput", "name": "permit", "type": "tuple"}], "name": "addValidatorKeysStETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "keysCount", "type": "uint256"}, {"internalType": "bytes", "name": "publicKeys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}, {"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "internalType": "struct ICSAccounting.PermitInput", "name": "permit", "type": "tuple"}], "name": "addValidatorKeysWstETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "cancelELRewardsStealingPenalty", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "changeNodeOperatorRewardAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "stETHAmount", "type": "uint256"}, {"internalType": "uint256", "name": "cumulativeFeeShares", "type": "uint256"}, {"internalType": "bytes32[]", "name": "rewardsProof", "type": "bytes32[]"}], "name": "claimRewardsStETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "stEthAmount", "type": "uint256"}, {"internalType": "uint256", "name": "cumulativeFeeShares", "type": "uint256"}, {"internalType": "bytes32[]", "name": "rewardsProof", "type": "bytes32[]"}], "name": "claimRewardsUnstETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "wstETHAmount", "type": "uint256"}, {"internalType": "uint256", "name": "cumulativeFeeShares", "type": "uint256"}, {"internalType": "bytes32[]", "name": "rewardsProof", "type": "bytes32[]"}], "name": "claimRewardsWstETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxItems", "type": "uint256"}], "name": "cleanDepositQueue", "outputs": [{"internalType": "uint256", "name": "removed", "type": "uint256"}, {"internalType": "uint256", "name": "lastRemovedAtDepth", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "compensateELRewardsStealingPenalty", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "confirmNodeOperatorManagerAddressChange", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "confirmNodeOperatorRewardAddressChange", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "nodeOperatorIds", "type": "bytes"}, {"internalType": "bytes", "name": "vettedSigningKeysCounts", "type": "bytes"}], "name": "decreaseVettedSigningKeysCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "depositETH", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "depositQueue", "outputs": [{"internalType": "uint128", "name": "head", "type": "uint128"}, {"internalType": "uint128", "name": "tail", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint128", "name": "index", "type": "uint128"}], "name": "depositQueueItem", "outputs": [{"internalType": "<PERSON><PERSON>", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "stETHAmount", "type": "uint256"}, {"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "internalType": "struct ICSAccounting.PermitInput", "name": "permit", "type": "tuple"}], "name": "depositStETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "wstETHAmount", "type": "uint256"}, {"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "internalType": "struct ICSAccounting.PermitInput", "name": "permit", "type": "tuple"}], "name": "depositWstETH", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "earlyAdoption", "outputs": [{"internalType": "contract ICSEarlyAdoption", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getActiveNodeOperatorsCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "getNodeOperator", "outputs": [{"components": [{"internalType": "uint32", "name": "totalAddedKeys", "type": "uint32"}, {"internalType": "uint32", "name": "totalWithdrawnKeys", "type": "uint32"}, {"internalType": "uint32", "name": "totalDepositedKeys", "type": "uint32"}, {"internalType": "uint32", "name": "totalVettedKeys", "type": "uint32"}, {"internalType": "uint32", "name": "stuckValidatorsCount", "type": "uint32"}, {"internalType": "uint32", "name": "depositableValidatorsCount", "type": "uint32"}, {"internalType": "uint32", "name": "targetLimit", "type": "uint32"}, {"internalType": "uint8", "name": "targetLimitMode", "type": "uint8"}, {"internalType": "uint32", "name": "totalExitedKeys", "type": "uint32"}, {"internalType": "uint32", "name": "enqueuedCount", "type": "uint32"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "address", "name": "proposedRewardAddress", "type": "address"}, {"internalType": "bool", "name": "extendedManagerPermissions", "type": "bool"}], "internalType": "struct NodeOperator", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offset", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getNodeOperatorIds", "outputs": [{"internalType": "uint256[]", "name": "nodeOperatorIds", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "getNodeOperatorIsActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "getNodeOperatorNonWithdrawnKeys", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "getNodeOperatorSummary", "outputs": [{"internalType": "uint256", "name": "targetLimitMode", "type": "uint256"}, {"internalType": "uint256", "name": "targetValidatorsCount", "type": "uint256"}, {"internalType": "uint256", "name": "stuckValidatorsCount", "type": "uint256"}, {"internalType": "uint256", "name": "refundedValidatorsCount", "type": "uint256"}, {"internalType": "uint256", "name": "stuckPenaltyEndTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "totalExitedValidators", "type": "uint256"}, {"internalType": "uint256", "name": "totalDepositedValidators", "type": "uint256"}, {"internalType": "uint256", "name": "depositableValidatorsCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getNodeOperatorsCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getResumeSinceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "startIndex", "type": "uint256"}, {"internalType": "uint256", "name": "keysCount", "type": "uint256"}], "name": "getSigningKeys", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "startIndex", "type": "uint256"}, {"internalType": "uint256", "name": "keysCount", "type": "uint256"}], "name": "getSigningKeysWithSignatures", "outputs": [{"internalType": "bytes", "name": "keys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getStakingModuleSummary", "outputs": [{"internalType": "uint256", "name": "totalExitedValidators", "type": "uint256"}, {"internalType": "uint256", "name": "totalDepositedValidators", "type": "uint256"}, {"internalType": "uint256", "name": "depositableValidatorsCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getType", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_accounting", "type": "address"}, {"internalType": "address", "name": "_earlyAdoption", "type": "address"}, {"internalType": "uint256", "name": "_keyRemovalCharge", "type": "uint256"}, {"internalType": "address", "name": "admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "isPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "keyIndex", "type": "uint256"}], "name": "isValidatorSlashed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "keyIndex", "type": "uint256"}], "name": "isValidatorWithdrawn", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "keyRemovalCharge", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "normalizeQueue", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "depositsCount", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "obtainDepositData", "outputs": [{"internalType": "bytes", "name": "publicKeys", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "onExitedAndStuckValidatorsCountsUpdated", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "totalShares", "type": "uint256"}], "name": "onRewardsMinted", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "onWithdrawalCredentialsChanged", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "pauseFor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "address", "name": "proposedAddress", "type": "address"}], "name": "proposeNodeOperatorManagerAddressChange", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "address", "name": "proposedAddress", "type": "address"}], "name": "proposeNodeOperatorRewardAddressChange", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "publicRelease", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "recoverERC1155", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "recoverERC20", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "recoverERC721", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "recoverEther", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "startIndex", "type": "uint256"}, {"internalType": "uint256", "name": "keysCount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "reportELRewardsStealingPenalty", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}], "name": "resetNodeOperatorManagerAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "resume", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "setKeyRemovalCharge", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "nodeOperatorIds", "type": "uint256[]"}], "name": "settleELRewardsStealingPenalty", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "keyIndex", "type": "uint256"}], "name": "submitInitialSlashing", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "keyIndex", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bool", "name": "isSlashed", "type": "bool"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "exitedValidatorsKeysCount", "type": "uint256"}, {"internalType": "uint256", "name": "stuckValidatorsKeysCount", "type": "uint256"}], "name": "unsafeUpdateValidatorsCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "nodeOperatorIds", "type": "bytes"}, {"internalType": "bytes", "name": "exitedValidatorsCounts", "type": "bytes"}], "name": "updateExitedValidatorsCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "updateRefundedValidatorsCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "nodeOperatorIds", "type": "bytes"}, {"internalType": "bytes", "name": "stuckValidatorsCounts", "type": "bytes"}], "name": "updateStuckValidatorsCount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "nodeOperatorId", "type": "uint256"}, {"internalType": "uint256", "name": "targetLimitMode", "type": "uint256"}, {"internalType": "uint256", "name": "targetLimit", "type": "uint256"}], "name": "updateTargetValidatorsLimits", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]