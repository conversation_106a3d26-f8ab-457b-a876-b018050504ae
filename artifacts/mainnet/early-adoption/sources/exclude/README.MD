# Source files to be used for exclusion

## [csm-testnet-bad-performers.csv](csm-testnet-bad-performers.csv)

Bad performers with sufficient participation time from the CSM Testnet. Complied from [raw sources](../../raw_sources/csm_testnet_performance/README.MD).

## [ever-slashed.csv](ever-slashed.csv)

Addresses associated with the slashed Ethereum validators on the Ethereum Mainnet. Compiled using https://gist.github.com/vgorkavenko/ca31cd050ec40dde0980064b1081ea39

## [pro-node-operators.csv](pro-node-operators.csv)

Addresses associated with the Professional Node Operators. Complied from [raw sources](../../raw_sources/pro_operators/README.MD).

## [rocketpool-solo-stakers-deposit-addresses.csv](rocketpool-solo-stakers-deposit-addresses.csv)

To ensure single entry of the RP solo stakers deposit addresses should be excluded from the other lists since Withdrawal Addresses are used instead

## [rated-solo-wc-addresses.csv](rated-solo-wc-addresses.csv)

To ensure single entry of the Rated solo stakers WC addresses should be excluded from the other lists since deposit addresses are used instead.
Rocket Pool withdrawal addresses were not included into the file due to their inclusion instead of deposit addresses.
