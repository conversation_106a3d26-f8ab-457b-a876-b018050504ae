// SPDX-FileCopyrightText: 2025 Lido <<EMAIL>>
// SPDX-License-Identifier: GPL-3.0

pragma solidity 0.8.24;

import "forge-std/Test.sol";
import { CSAccounting } from "../src/CSAccounting.sol";
import { OssifiableProxy } from "../src/lib/proxy/OssifiableProxy.sol";
import { ICSBondCurve } from "../src/interfaces/ICSBondCurve.sol";
import { ILidoLocator } from "../src/interfaces/ILidoLocator.sol";
import { IStETH } from "../src/interfaces/IStETH.sol";
import { IWstETH } from "../src/interfaces/IWstETH.sol";
import { IWithdrawalQueue } from "../src/interfaces/IWithdrawalQueue.sol";
import { CSFeeDistributor } from "../src/CSFeeDistributor.sol";

// CSAccounting Initialization Race Condition POC
// Demonstrates critical vulnerabilities in the initialization pattern
// This POC works standalone without requiring environment variables or forks

contract DeploymentBaseTest_PoC is Test {
    // Mock contracts for dependencies
    address constant LIDO_LOCATOR = address(0x1);
    address constant MODULE = address(0x2);
    address constant FEE_DISTRIBUTOR = address(0x3);
    address constant STETH = address(0x4);
    address constant WSTETH = address(0x5);
    address constant WITHDRAWAL_QUEUE = address(0x6);
    address constant BURNER = address(0x7);

    // Test actors
    address deployer = address(0x100);
    address attacker = address(0x1337);
    address legitimateAdmin = address(0x999);
    address proxyAdmin = address(0x400);

    function setUp() public {
        // Setup mock contracts for LidoLocator
        vm.mockCall(
            LIDO_LOCATOR,
            abi.encodeWithSignature("lido()"),
            abi.encode(STETH)
        );
        vm.mockCall(
            LIDO_LOCATOR,
            abi.encodeWithSignature("wstETH()"),
            abi.encode(WSTETH)
        );
        vm.mockCall(
            LIDO_LOCATOR,
            abi.encodeWithSignature("withdrawalQueue()"),
            abi.encode(WITHDRAWAL_QUEUE)
        );
        vm.mockCall(
            LIDO_LOCATOR,
            abi.encodeWithSignature("burner()"),
            abi.encode(BURNER)
        );

        // Mock WithdrawalQueue.WSTETH() call
        vm.mockCall(
            WITHDRAWAL_QUEUE,
            abi.encodeWithSignature("WSTETH()"),
            abi.encode(WSTETH)
        );

        // Mock approve calls to prevent reverts
        vm.mockCall(STETH, abi.encodeWithSignature("approve(address,uint256)"), abi.encode(true));
    }

    function test_PoC() public {
        console.log("=== CSAccounting Legitimate Upgrade Flow Broken POC ===");

        // Deploy a fresh CSAccounting proxy to demonstrate the vulnerability
        vm.prank(deployer);
        CSAccounting accountingImpl = new CSAccounting(
            LIDO_LOCATOR,
            MODULE,
            FEE_DISTRIBUTOR,
            1 days,  // minBondLockPeriod
            365 days // maxBondLockPeriod
        );

        vm.prank(deployer);
        OssifiableProxy testProxy = new OssifiableProxy(
            address(accountingImpl),
            proxyAdmin, // proxy admin
            ""
        );

        CSAccounting testAccounting = CSAccounting(address(testProxy));

        console.log("1. Fresh proxy deployed, initialized version:", testAccounting.getInitializedVersion());

        // === POC: Legitimate Upgrade Flow Broken ===
        console.log("\n=== Demonstrating Broken Legitimate Upgrade Flow ===");

        // Normal deployment: initialize is called first (as intended)
        ICSBondCurve.BondCurveIntervalInput[] memory legitimateBondCurve =
            _createLegitimateDefaultBondCurve();

        console.log("Calling initialize() first (normal deployment)...");
        testAccounting.initialize(
            legitimateBondCurve,
            legitimateAdmin,
            7 days,
            address(0x888)
        );

        console.log("Contract initialized version:", testAccounting.getInitializedVersion());
        console.log("Admin role granted to legitimate admin:", testAccounting.hasRole(testAccounting.DEFAULT_ADMIN_ROLE(), legitimateAdmin));

        // PROBLEM: Now finalizeUpgradeV2 can never be called for legitimate upgrades
        _setupLegacyBondCurves(address(testAccounting), 2);

        // Create bond curves for the upgrade attempt
        ICSBondCurve.BondCurveIntervalInput[][] memory upgradeBondCurves =
            _createMaliciousBondCurves();

        console.log("Attempting finalizeUpgradeV2() for legitimate upgrade...");
        vm.expectRevert(); // Should revert with InvalidInitialization() - both use reinitializer(2)
        testAccounting.finalizeUpgradeV2(upgradeBondCurves);
        console.log("[+] finalizeUpgradeV2() permanently blocked");

        // Demonstrate the issue: all operators stuck with default curve
        console.log("Bond curve ID for operator 0:", testAccounting.getBondCurveId(0));
        console.log("Bond curve ID for operator 1:", testAccounting.getBondCurveId(1));
        console.log("Bond curve ID for operator 999:", testAccounting.getBondCurveId(999));

        console.log("\n=== POC Results ===");
        console.log("[+] Normal initialize() works correctly");
        console.log("[+] But finalizeUpgradeV2() is permanently blocked");
        console.log("[+] Legacy bond curve migration cannot happen");
        console.log("[+] All operators stuck with default curve (curveId == 0)");
        console.log("[+] Expected migration from BondCurve -> BondCurveInterval[] broken");

        console.log("\n=== VULNERABILITY CONFIRMED ===");
        console.log("Both initialize() and finalizeUpgradeV2() use reinitializer(2)");
        console.log("After normal initialization, finalizeUpgradeV2() is permanently blocked");
        console.log("This breaks the legitimate upgrade flow:");
        console.log("- Legacy bond curve migration cannot happen");
        console.log("- All operators remain stuck with default curve settings");
        console.log("- Upgrade path is fundamentally broken");
        console.log("\nRECOMMENDED FIXES:");
        console.log("- Use different reinitializer versions (e.g., reinitializer(3))");
        console.log("- Perform migration in upgrader script via delegatecall");
        console.log("- Remove public finalizeUpgradeV2() and handle migration internally");
    }

    // Helper functions
    function _setupLegacyBondCurves(address target, uint256 count) internal {
        bytes32 storageSlot = 0x8f22e270e477f5becb8793b61d439ab7ae990ed8eba045eb72061c0e6cfe1500;
        vm.store(target, storageSlot, bytes32(count));
    }

    function _createMaliciousBondCurves()
        internal
        pure
        returns (ICSBondCurve.BondCurveIntervalInput[][] memory)
    {
        ICSBondCurve.BondCurveIntervalInput[][] memory bondCurves =
            new ICSBondCurve.BondCurveIntervalInput[][](2);

        // High bond requirement curve
        bondCurves[0] = new ICSBondCurve.BondCurveIntervalInput[](1);
        bondCurves[0][0] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 1,
            trend: 100 ether
        });

        // Low bond requirement curve
        bondCurves[1] = new ICSBondCurve.BondCurveIntervalInput[](1);
        bondCurves[1][0] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 1,
            trend: 1 ether
        });

        return bondCurves;
    }

    function _createLegitimateDefaultBondCurve()
        internal
        pure
        returns (ICSBondCurve.BondCurveIntervalInput[] memory)
    {
        ICSBondCurve.BondCurveIntervalInput[] memory bondCurve =
            new ICSBondCurve.BondCurveIntervalInput[](2);

        bondCurve[0] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 1,
            trend: 2 ether
        });

        bondCurve[1] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 5,
            trend: 1 ether
        });

        return bondCurve;
    }
}