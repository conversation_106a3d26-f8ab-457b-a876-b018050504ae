// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.24;

import "forge-std/Test.sol";

import { DeploymentFixtures } from "./helpers/Fixtures.sol";
import { DeployParams } from "./../script/DeployBase.s.sol";
import { Utilities } from "./helpers/Utilities.sol";
import { CSAccounting } from "../src/CSAccounting.sol";
import { OssifiableProxy } from "../src/lib/proxy/OssifiableProxy.sol";
import { ICSBondCurve } from "../src/interfaces/ICSBondCurve.sol";

// CSAccounting Legitimate Upgrade Flow Broken POC
// Uses sponsor's exact deployment framework
// run `just test-poc` to run the tests here

contract DeploymentBaseTest_PoC is Test, Utilities, DeploymentFixtures {
    DeployParams internal deployParams;
    uint256 adminsCount;

    // Test actors
    address legitimateAdmin = address(0x999);

    function setUp() public {
        Env memory env = envVars();
        vm.createSelectFork(env.RPC_URL);
        initializeFromDeployment();

        // Skip deployParams parsing as it's complex and not needed for our POC
        // deployParams = parseDeployParams(env.DEPLOY_CONFIG);

        adminsCount = block.chainid == 1 ? 1 : 2;
    }

    function test_PoC() public {
        console.log("=== CSAccounting Legitimate Upgrade Flow Broken POC ===");
        console.log("Using sponsor's deployment framework with fresh contracts");

        // Deploy fresh CSAccounting contracts using existing infrastructure
        // This demonstrates the vulnerability would occur in any new deployment
        CSAccounting freshAccountingImpl = new CSAccounting(
            address(locator),           // Use existing locator from deployment
            address(csm),              // Use existing CSM from deployment
            address(feeDistributor),   // Use existing fee distributor from deployment
            1 days,                    // minBondLockPeriod
            365 days                   // maxBondLockPeriod
        );

        // Deploy fresh proxy to demonstrate initialization vulnerability
        OssifiableProxy freshProxy = new OssifiableProxy(
            address(freshAccountingImpl),
            address(this), // proxy admin
            ""
        );

        CSAccounting freshAccounting = CSAccounting(address(freshProxy));

        console.log("1. Fresh contracts deployed, initialized version:", freshAccounting.getInitializedVersion());

        // === Demonstrating Broken Legitimate Upgrade Flow ===
        console.log("\n=== Demonstrating Broken Legitimate Upgrade Flow ===");

        // Normal deployment: initialize is called first (as intended)
        ICSBondCurve.BondCurveIntervalInput[] memory legitimateBondCurve =
            _createLegitimateDefaultBondCurve();

        console.log("Calling initialize() first (normal deployment)...");
        freshAccounting.initialize(
            legitimateBondCurve,
            legitimateAdmin,
            7 days,
            address(0x888)
        );

        console.log("Contract initialized version:", freshAccounting.getInitializedVersion());
        console.log("Admin role granted to legitimate admin:", freshAccounting.hasRole(freshAccounting.DEFAULT_ADMIN_ROLE(), legitimateAdmin));

        // PROBLEM: Now finalizeUpgradeV2 can never be called for legitimate upgrades
        _setupLegacyBondCurves(address(freshAccounting), 2);

        // Create bond curves for the upgrade attempt
        ICSBondCurve.BondCurveIntervalInput[][] memory upgradeBondCurves =
            _createUpgradeBondCurves();

        console.log("Attempting finalizeUpgradeV2() for legitimate upgrade...");
        vm.expectRevert(); // Should revert with InvalidInitialization() - both use reinitializer(2)
        freshAccounting.finalizeUpgradeV2(upgradeBondCurves);
        console.log("[+] finalizeUpgradeV2() permanently blocked");

        // Demonstrate the issue: all operators stuck with default curve
        console.log("Bond curve ID for operator 0:", freshAccounting.getBondCurveId(0));
        console.log("Bond curve ID for operator 1:", freshAccounting.getBondCurveId(1));
        console.log("Bond curve ID for operator 999:", freshAccounting.getBondCurveId(999));

        console.log("\n=== POC Results ===");
        console.log("[+] Normal initialize() works correctly");
        console.log("[+] But finalizeUpgradeV2() is permanently blocked");
        console.log("[+] Legacy bond curve migration cannot happen");
        console.log("[+] All operators stuck with default curve (curveId == 0)");
        console.log("[+] Expected migration from BondCurve -> BondCurveInterval[] broken");

        console.log("\n=== VULNERABILITY CONFIRMED ===");
        console.log("Both initialize() and finalizeUpgradeV2() use reinitializer(2)");
        console.log("After normal initialization, finalizeUpgradeV2() is permanently blocked");
        console.log("This breaks the legitimate upgrade flow:");
        console.log("- Legacy bond curve migration cannot happen");
        console.log("- All operators remain stuck with default curve settings");
        console.log("- Upgrade path is fundamentally broken");
        console.log("\nRECOMMENDED FIXES:");
        console.log("- Use different reinitializer versions (e.g., reinitializer(3))");
        console.log("- Perform migration in upgrader script via delegatecall");
        console.log("- Remove public finalizeUpgradeV2() and handle migration internally");
    }

    // Helper functions
    function _setupLegacyBondCurves(address target, uint256 count) internal {
        bytes32 storageSlot = 0x8f22e270e477f5becb8793b61d439ab7ae990ed8eba045eb72061c0e6cfe1500;
        vm.store(target, storageSlot, bytes32(count));
    }

    function _createUpgradeBondCurves()
        internal
        pure
        returns (ICSBondCurve.BondCurveIntervalInput[][] memory)
    {
        ICSBondCurve.BondCurveIntervalInput[][] memory bondCurves =
            new ICSBondCurve.BondCurveIntervalInput[][](2);

        // Curve 1: Standard bond requirement
        bondCurves[0] = new ICSBondCurve.BondCurveIntervalInput[](1);
        bondCurves[0][0] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 1,
            trend: 32 ether
        });

        // Curve 2: Lower bond requirement
        bondCurves[1] = new ICSBondCurve.BondCurveIntervalInput[](1);
        bondCurves[1][0] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 1,
            trend: 16 ether
        });

        return bondCurves;
    }

    function _createLegitimateDefaultBondCurve()
        internal
        pure
        returns (ICSBondCurve.BondCurveIntervalInput[] memory)
    {
        ICSBondCurve.BondCurveIntervalInput[] memory bondCurve =
            new ICSBondCurve.BondCurveIntervalInput[](2);

        bondCurve[0] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 1,
            trend: 2 ether
        });

        bondCurve[1] = ICSBondCurve.BondCurveIntervalInput({
            minKeysCount: 5,
            trend: 1 ether
        });

        return bondCurve;
    }

}